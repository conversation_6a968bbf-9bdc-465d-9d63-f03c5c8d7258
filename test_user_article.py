#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于spider_readnum.py测试用户提供的文章URL
"""

import re
import requests
import json
import winreg
import ctypes
import contextlib
from urllib.parse import urlparse, parse_qs

# --- 代理管理 ---
INTERNET_OPTION_SETTINGS_CHANGED = 39
INTERNET_OPTION_REFRESH = 37
FWinHttpSetDefaultProxyConfiguration = ctypes.windll.winhttp.WinHttpSetDefaultProxyConfiguration
InternetSetOption = ctypes.windll.wininet.InternetSetOptionW

@contextlib.contextmanager
def manage_system_proxy(proxy_address="127.0.0.1:8080"):
    """
    一个上下文管理器，用于在代码块执行期间临时禁用指定的系统代理。
    """
    original_state = {"enabled": False, "server": ""}
    was_active = False
    key = None
    try:
        # 打开注册表项
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Internet Settings", 0, winreg.KEY_READ | winreg.KEY_WRITE)
        
        # 读取原始代理状态
        try:
            original_state["enabled"] = winreg.QueryValueEx(key, "ProxyEnable")[0] == 1
            original_state["server"] = winreg.QueryValueEx(key, "ProxyServer")[0]
        except FileNotFoundError:
            # 如果值不存在，则代理是禁用的
            pass

        # 检查代理是否是我们需要禁用的那个
        if original_state["enabled"] and original_state["server"] == proxy_address:
            was_active = True
            print(f"检测到活动代理 {proxy_address}，正在临时禁用...")
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            # 通知系统设置已更改
            InternetSetOption(0, INTERNET_OPTION_SETTINGS_CHANGED, 0, 0)
            InternetSetOption(0, INTERNET_OPTION_REFRESH, 0, 0)
        
        yield # 执行主代码块

    finally:
        # 恢复原始代理设置
        if was_active and key:
            print(f"正在恢复代理 {proxy_address}...")
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
            InternetSetOption(0, INTERNET_OPTION_SETTINGS_CHANGED, 0, 0)
            InternetSetOption(0, INTERNET_OPTION_REFRESH, 0, 0)
        if key:
            winreg.CloseKey(key)

def test_user_article():
    """测试用户提供的文章URL"""
    
    # 用户提供的文章URL
    url = "https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521211&idx=1&sn=625bcc844779e023278bc4d6ebf99443&chksm=cf5c50f0313e08cf332dd7ce8f84e93453c59278c5b9fdd5e1c8da6eeeb9f556c8871d2c4502&scene=126&sessionid=1754029539&subscene=7&clicktime=1754029544&enterid=1754029544&key=daf9bdc5abc4e8d0afabcb18c34aa872dca70ff51a36cd36e0063459450f20f90a31b0acb5c72b850dce43730d8a6f8fd9ad4f2bd72d69d52f75fc0b610d9a20a589cd6841dc01ed0da5acb7a2e02fb64e9f510882f26d829de515d71fb275244a7adbef53c99f391444dd176c40654afa60a10e04f7b7250a295cb2ec591ddb&ascene=0&uin=Mjc3NDgzMTgyNQ%3D%3D&devicetype=Windows+11+x64&version=63090c37&lang=zh_CN&countrycode=CN&exportkey=n_ChQIAhIQLuDIFwAc22mgqRsdlRwIehLmAQIE97dBBAEAAAAAAAsWJnKUyRIAAAAOpnltbLcz9gKNyK89dVj0YIRFOpE96bydDd04g0Vm8dM3xpF0PsyzSAPYCRjjaUlYWAWB76Fc6X6FSoYQvq%2BW%2FF8pXZgtANNVRP34qUKemBOuzURu7cxC%2FSQN4VyK%2FA1foGFlJGq0xz6habl5iUhre4RLcrA%2Bw%2Fs2Ega602DRxJtw5kJs12%2B7DQnv7%2FtYeb4hO4aNqygU4f8%2FxOOasnkpT4tSHaoXqJwrG5KjK4ayaLSTeOKdSD2vWlyw6op4WFyaf7NNDm4bOf7t32SgdLSI&acctmode=0&pass_ticket=qlu4yJhrXe4MCkwC60ENqA8wBPKIBdRGxHStOQ6vcK5GwsB3E8URW1sk8HMFoem0&wx_header=1&fasttmpl_type=0&fasttmpl_fullversion=7841198-zh_CN-zip&fasttmpl_flag=1"
    
    print("🔍 测试用户提供的文章URL")
    print("="*60)
    print(f"📖 文章URL: {url[:100]}...")
    
    # 从wechat_keys.txt读取最新的认证信息
    try:
        with open('wechat_keys.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到最新的记录
        records = content.split('=' * 60)
        latest_record = None
        
        for record in reversed(records):
            if 'x-wechat-key:' in record and 'Cookies:' in record:
                latest_record = record
                break
        
        if not latest_record:
            print("❌ 未找到有效的认证信息")
            return False
        
        # 解析cookies
        cookie_match = re.search(r'Cookies:\s*(.+)', latest_record)
        if not cookie_match:
            print("❌ 未找到cookies")
            return False
        
        cookies = cookie_match.group(1).strip()
        
        # 解析headers
        headers = {}
        header_lines = re.findall(r'\s+([^:]+):\s*(.+)', latest_record)
        for header_name, header_value in header_lines:
            headers[header_name.strip()] = header_value.strip()
        
        print(f"✅ 成功加载认证信息")
        print(f"   cookies长度: {len(cookies)}")
        print(f"   headers数量: {len(headers)}")
        
        if 'x-wechat-key' in headers:
            print(f"   x-wechat-key: {headers['x-wechat-key'][:30]}...")
        else:
            print("   ⚠️ 缺少x-wechat-key")
        
    except Exception as e:
        print(f"❌ 读取认证信息失败: {e}")
        return False
    
    # 构建请求headers（基于spider_readnum.py的成功实现）
    request_headers = {
        'cache-control': 'max-age=0',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c37) XWEB/14315 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-dest': 'document',
        'accept-encoding': 'gzip, deflate, br',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'u=0, i',
        'Cookie': cookies
    }
    
    # 添加从抓包获取的关键headers
    for key, value in headers.items():
        request_headers[key] = value
    
    print(f"\n📡 发送请求...")
    print(f"🔍 使用headers: {list(request_headers.keys())}")
    
    # 使用上下文管理器来自动处理代理
    with manage_system_proxy("127.0.0.1:8080"):
        print("正在发送网络请求...")
        response = requests.get(url, headers=request_headers)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
        
        html_content = response.text
        print(f"✅ 请求成功，HTML长度: {len(html_content)} 字符")
        
        # 保存原始的响应结果
        with open('user_article_response.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("🔍 HTML内容已保存到: user_article_response.html")
        
        # 使用正则表达式提取文章标题、作者和内容
        title_match = re.search(r'<meta property="og:title" content="(.*?)"', html_content)
        author_match = re.search(r'<meta property="og:article:author" content="(.*?)"', html_content)
        content_match = re.search(r'id="js_content".*?>(.*?)</div>', html_content, re.S)

        title = title_match.group(1) if title_match else "未找到标题"
        author = author_match.group(1) if author_match else "未找到作者"
        # 简单清理一下HTML标签
        content = re.sub(r'<.*?>', '', content_match.group(1)) if content_match else "未找到内容"

        print(f"\n📝 文章信息:")
        print(f"   标题: {title}")
        print(f"   作者: {author}")
        print(f"   内容长度: {len(content)} 字符")

        # 提取阅读数、点赞数、在看数和分享数（使用spider_readnum.py中验证成功的正则表达式）
        read_num_match = re.search(r"var cgiData = {[^}]*?read_num: '(\d+)'", html_content)
        like_num_match = re.search(r"window.appmsg_bar_data = {[^}]*?like_count: '(\d+)'", html_content)
        old_like_num_match = re.search(r"window.appmsg_bar_data = {[^}]*?old_like_count: '(\d+)'", html_content)
        share_count_match = re.search(r"window.appmsg_bar_data = {[^}]*?share_count: '(\d+)'", html_content)

        read_count = int(read_num_match.group(1)) if read_num_match else 0
        like_count = int(like_num_match.group(1)) if like_num_match else 0
        old_like_count = int(old_like_num_match.group(1)) if old_like_num_match else 0
        share_count = int(share_count_match.group(1)) if share_count_match else 0

        print(f"\n📊 统计数据:")
        print(f"   📖 阅读量: {read_count}")
        print(f"   👍 点赞数: {like_count}")
        print(f"   💖 历史点赞数: {old_like_count}")
        print(f"   📤 分享数: {share_count}")

        # 组织成字典
        article_data = {
            "title": title.strip(),
            "author": author.strip(),
            "content": content.strip()[:200] + "..." if len(content) > 200 else content.strip(),
            "read_count": read_count,
            "like_count": like_count,
            "old_like_count": old_like_count,
            "share_count": share_count
        }

        # 将字典保存为 JSON 文件
        with open('user_article.json', 'w', encoding='utf-8') as f:
            json.dump(article_data, f, ensure_ascii=False, indent=4)

        print(f"\n✅ 文章数据已成功保存到 user_article.json 文件中。")
        print(f"✅ 原始响应已保存到 user_article_response.html 文件中。")
        
        # 检查是否成功获取统计数据
        if read_count > 0 or like_count > 0 or share_count > 0:
            print(f"\n🎉 成功获取统计数据！")
            return True
        else:
            print(f"\n⚠️ 所有统计数据都为0，可能需要检查HTML内容")
            return False

if __name__ == "__main__":
    success = test_user_article()
    if success:
        print("\n🎉 测试成功！可以获取阅读量等统计数据")
    else:
        print("\n⚠️ 测试未完全成功，请检查生成的HTML文件")
