#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试指定文章的阅读量提取
使用用户提供的具体文章URL
"""

import re
import requests
import json
from urllib.parse import urlparse, parse_qs
from read_cookie import ReadCookie
from batch_readnum_spider import Bat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_specific_article():
    """测试指定文章的阅读量提取"""
    
    # 用户提供的文章URL
    article_url = "https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521211&idx=1&sn=625bcc844779e023278bc4d6ebf99443&chksm=cf5c50f0313e08cf332dd7ce8f84e93453c59278c5b9fdd5e1c8da6eeeb9f556c8871d2c4502&scene=126&sessionid=1754029539&subscene=7&clicktime=1754029544&enterid=1754029544&key=daf9bdc5abc4e8d0afabcb18c34aa872dca70ff51a36cd36e0063459450f20f90a31b0acb5c72b850dce43730d8a6f8fd9ad4f2bd72d69d52f75fc0b610d9a20a589cd6841dc01ed0da5acb7a2e02fb64e9f510882f26d829de515d71fb275244a7adbef53c99f391444dd176c40654afa60a10e04f7b7250a295cb2ec591ddb&ascene=0&uin=Mjc3NDgzMTgyNQ%3D%3D&devicetype=Windows+11+x64&version=63090c37&lang=zh_CN&countrycode=CN&exportkey=n_ChQIAhIQLuDIFwAc22mgqRsdlRwIehLmAQIE97dBBAEAAAAAAAsWJnKUyRIAAAAOpnltbLcz9gKNyK89dVj0YIRFOpE96bydDd04g0Vm8dM3xpF0PsyzSAPYCRjjaUlYWAWB76Fc6X6FSoYQvq%2BW%2FF8pXZgtANNVRP34qUKemBOuzURu7cxC%2FSQN4VyK%2FA1foGFlJGq0xz6habl5iUhre4RLcrA%2Bw%2Fs2Ega602DRxJtw5kJs12%2B7DQnv7%2FtYeb4hO4aNqygU4f8%2FxOOasnkpT4tSHaoXqJwrG5KjK4ayaLSTeOKdSD2vWlyw6op4WFyaf7NNDm4bOf7t32SgdLSI&acctmode=0&pass_ticket=qlu4yJhrXe4MCkwC60ENqA8wBPKIBdRGxHStOQ6vcK5GwsB3E8URW1sk8HMFoem0&wx_header=1&fasttmpl_type=0&fasttmpl_fullversion=7841198-zh_CN-zip&fasttmpl_flag=1"
    
    print("🔍 测试指定文章的阅读量提取")
    print("="*60)
    print(f"📖 文章URL: {article_url[:100]}...")
    
    try:
        # 解析URL参数
        parsed_url = urlparse(article_url)
        params = parse_qs(parsed_url.query)
        
        print(f"\n📋 解析到的关键参数:")
        key_params = ['__biz', 'mid', 'idx', 'sn', 'key', 'pass_ticket', 'exportkey']
        for param in key_params:
            if param in params:
                value = params[param][0]
                if len(value) > 50:
                    print(f"   {param}: {value[:30]}...")
                else:
                    print(f"   {param}: {value}")
        
        # 加载认证信息
        print(f"\n🔑 加载认证信息...")
        cookie_reader = ReadCookie()
        result = cookie_reader.get_latest_cookies()
        
        if not result:
            print("❌ 未能加载认证信息")
            return False
        
        print("✅ 认证信息加载成功")
        
        # 检查关键headers
        headers = result.get('headers', {})
        if 'x-wechat-key' in headers:
            print(f"✅ x-wechat-key: {headers['x-wechat-key'][:30]}...")
        else:
            print("⚠️ 缺少 x-wechat-key")
        
        # 构建请求headers
        request_headers = {
            'cache-control': 'max-age=0',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c37) XWEB/14315 Flue',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-dest': 'document',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=0, i',
            'Cookie': result['cookie_str']
        }
        
        # 添加从抓包获取的关键headers
        for key, value in headers.items():
            request_headers[key] = value
        
        print(f"\n📡 发送请求...")
        print(f"🔍 使用headers: {list(request_headers.keys())}")
        
        # 发送请求
        response = requests.get(article_url, headers=request_headers, verify=False, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
        
        html_content = response.text
        print(f"✅ 请求成功，HTML长度: {len(html_content)} 字符")
        
        # 保存HTML用于调试
        debug_file = f"./debug/specific_article_{article_url.split('mid=')[1].split('&')[0]}.html"
        import os
        os.makedirs("./debug", exist_ok=True)
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"🔍 HTML内容已保存到: {debug_file}")
        
        # 提取文章标题
        title_match = re.search(r'<meta property="og:title" content="(.*?)"', html_content)
        title = title_match.group(1) if title_match else "未找到标题"
        print(f"📝 文章标题: {title}")
        
        # 使用spider_readnum.py中验证成功的正则表达式提取统计数据
        print(f"\n📊 提取统计数据...")
        
        # 提取阅读量
        read_num_match = re.search(r"var cgiData = {[^}]*?read_num: '(\d+)'", html_content)
        read_count = int(read_num_match.group(1)) if read_num_match else 0
        
        # 提取点赞数
        like_num_match = re.search(r"window\.appmsg_bar_data = {[^}]*?like_count: '(\d+)'", html_content)
        like_count = int(like_num_match.group(1)) if like_num_match else 0
        
        # 提取历史点赞数
        old_like_num_match = re.search(r"window\.appmsg_bar_data = {[^}]*?old_like_count: '(\d+)'", html_content)
        old_like_count = int(old_like_num_match.group(1)) if old_like_num_match else 0
        
        # 提取分享数
        share_count_match = re.search(r"window\.appmsg_bar_data = {[^}]*?share_count: '(\d+)'", html_content)
        share_count = int(share_count_match.group(1)) if share_count_match else 0
        
        # 显示结果
        print(f"\n📊 统计数据提取结果:")
        print(f"   📖 阅读量: {read_count}")
        print(f"   👍 点赞数: {like_count}")
        print(f"   💖 历史点赞数: {old_like_count}")
        print(f"   📤 分享数: {share_count}")
        
        # 检查是否成功获取数据
        if read_count > 0 or like_count > 0 or share_count > 0:
            print(f"\n🎉 成功获取统计数据！")
            print(f"✅ x-wechat-key 和其他参数工作正常")
            return True
        else:
            print(f"\n⚠️ 所有统计数据都为0")
            print(f"💡 可能的原因:")
            print(f"   1. 该文章确实没有公开统计数据")
            print(f"   2. 需要检查HTML中的JavaScript变量")
            print(f"   3. 可能需要更新抓包数据")
            
            # 搜索HTML中的相关变量
            print(f"\n🔍 搜索HTML中的统计变量...")
            cgi_data_match = re.search(r"var cgiData = \{[^}]*\}", html_content)
            if cgi_data_match:
                print(f"   cgiData: {cgi_data_match.group(0)[:100]}...")
            
            appmsg_bar_match = re.search(r"window\.appmsg_bar_data = \{[^}]*\}", html_content)
            if appmsg_bar_match:
                print(f"   appmsg_bar_data: {appmsg_bar_match.group(0)[:100]}...")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_specific_article()
    
    if success:
        print("\n🎉 指定文章测试成功！")
        print("\n💡 总结:")
        print("   ✅ 成功获取到统计数据")
        print("   ✅ x-wechat-key 和相关参数工作正常")
        print("   ✅ 可以用于批量抓取")
    else:
        print("\n⚠️ 指定文章测试未完全成功")
        print("\n💡 建议:")
        print("   1. 检查debug文件夹中的HTML文件")
        print("   2. 确认该文章是否真的有公开统计数据")
        print("   3. 可能需要重新抓包获取最新的认证信息")

if __name__ == "__main__":
    main()
